#!/usr/bin/env python3
# Copyright (c) Opendatalab. All rights reserved.
import os
import time
import argparse
import sys
from pathlib import Path
import torch.distributed as dist
from pdf2image import convert_from_path

from magic_pdf.data.data_reader_writer import FileBasedDataWriter, FileBasedDataReader
from magic_pdf.data.dataset import PymuDocDataset, ImageDataset
from magic_pdf.model.doc_analyze_by_custom_model_llm import doc_analyze_llm
from magic_pdf.model.custom_model import MonkeyOCR

# 定义任务指令
TASK_INSTRUCTIONS = {
    'text': 'Please output the text content from the image.',
    'formula': 'Please write out the expression of the formula in the image using LaTeX format.',
    'table': 'Please output the table in the image in LaTeX format.'
}

def parse_folder(folder_path, output_dir, config_path, task=None):
    """
    Parse all PDF and image files in a folder
    
    Args:
        folder_path: Input folder path
        output_dir: Output directory
        config_path: Configuration file path
        task: Optional task type for single task recognition
    """
    print(f"Starting to parse folder: {folder_path}")
    
    # Check if folder exists
    if not os.path.exists(folder_path):
        raise FileNotFoundError(f"Folder does not exist: {folder_path}")
    
    if not os.path.isdir(folder_path):
        raise ValueError(f"Path is not a directory: {folder_path}")
    
    # Find all supported files
    supported_extensions = {'.pdf', '.jpg', '.jpeg', '.png'}
    files_to_process = []
    
    for root, dirs, files in os.walk(folder_path):
        for file in files:
            file_path = os.path.join(root, file)
            file_ext = os.path.splitext(file)[1].lower()
            if file_ext in supported_extensions:
                files_to_process.append(file_path)
    
    files_to_process.sort()  # Sort for consistent processing order
    
    if not files_to_process:
        print("No supported files found in the folder.")
        return
    
    print(f"Found {len(files_to_process)} files to process:")
    for file_path in files_to_process:
        print(f"  - {file_path}")
    
    # Initialize model once for all files
    print("Loading model...")
    MonkeyOCR_model = MonkeyOCR(config_path)
    
    # Process each file
    successful_files = []
    failed_files = []
    
    for i, file_path in enumerate(files_to_process, 1):
        print(f"\n{'='*60}")
        print(f"Processing file {i}/{len(files_to_process)}: {os.path.basename(file_path)}")
        print(f"{'='*60}")
        
        try:
            if task:
                result_dir = single_task_recognition(file_path, output_dir, MonkeyOCR_model, task)
            else:
                result_dir = parse_pdf(file_path, output_dir, MonkeyOCR_model)
            
            successful_files.append(file_path)
            print(f"✅ Successfully processed: {os.path.basename(file_path)}")
            
        except Exception as e:
            failed_files.append((file_path, str(e)))
            print(f"❌ Failed to process {os.path.basename(file_path)}: {str(e)}")
    
    # Summary
    print(f"\n{'='*60}")
    print("PROCESSING SUMMARY")
    print(f"{'='*60}")
    print(f"Total files: {len(files_to_process)}")
    print(f"Successful: {len(successful_files)}")
    print(f"Failed: {len(failed_files)}")
    
    if failed_files:
        print("\nFailed files:")
        for file_path, error in failed_files:
            print(f"  - {os.path.basename(file_path)}: {error}")
    
    return output_dir

def single_task_recognition(input_file, output_dir, MonkeyOCR_model, task):
    """
    Single task recognition for specific content type
    
    Args:
        input_file: Input file path
        output_dir: Output directory
        MonkeyOCR_model: Pre-initialized model instance
        task: Task type ('text', 'formula', 'table')
    """
    print(f"Starting single task recognition: {task}")
    print(f"Processing file: {input_file}")
    
    # Check if input file exists
    if not os.path.exists(input_file):
        raise FileNotFoundError(f"Input file does not exist: {input_file}")
    
    # Get filename
    name_without_suff = '.'.join(os.path.basename(input_file).split(".")[:-1])
    
    # Prepare output directory
    local_md_dir = os.path.join(output_dir, name_without_suff)
    os.makedirs(local_md_dir, exist_ok=True)
    
    print(f"Output dir: {local_md_dir}")
    md_writer = FileBasedDataWriter(local_md_dir)
    
    # Get task instruction
    instruction = TASK_INSTRUCTIONS.get(task, TASK_INSTRUCTIONS['text'])
    
    # Check file type and prepare images
    file_extension = input_file.split(".")[-1].lower()
    images = []
    
    if file_extension == 'pdf':
        print("⚠️  WARNING: PDF input detected for single task recognition.")
        print("⚠️  WARNING: Converting all PDF pages to images for processing.")
        print("⚠️  WARNING: This may take longer and use more resources than image input.")
        print("⚠️  WARNING: Consider using individual images for better performance.")
        
        try:
            # Convert PDF pages to PIL images directly
            print("Converting PDF pages to images...")
            images = convert_from_path(input_file, dpi=150)
            print(f"Converted {len(images)} pages to images")
            
        except Exception as e:
            raise RuntimeError(f"Failed to convert PDF to images: {str(e)}")
            
    elif file_extension in ['jpg', 'jpeg', 'png']:
        # Load single image
        from PIL import Image
        images = [Image.open(input_file)]
    else:
        raise ValueError(f"Single task recognition supports PDF and image files, got: {file_extension}")
    
    # Start recognition
    print(f"Performing {task} recognition on {len(images)} image(s)...")
    start_time = time.time()
    
    try:
        # Prepare instructions for all images
        instructions = [instruction] * len(images)
        
        # Use chat model for single task recognition with PIL images directly
        responses = MonkeyOCR_model.chat_model.batch_inference(images, instructions)
        
        recognition_time = time.time() - start_time
        print(f"Recognition time: {recognition_time:.2f}s")
        
        # Combine results
        combined_result = responses[0]
        for i, response in enumerate(responses):
            if i > 0:
                combined_result = combined_result + "\n\n" + response
        
        # Save result
        result_filename = f"{name_without_suff}_{task}_result.md"
        md_writer.write(result_filename, combined_result.encode('utf-8'))
        
        print(f"Single task recognition completed!")
        print(f"Task: {task}")
        print(f"Processed {len(images)} image(s)")
        print(f"Result saved to: {os.path.join(local_md_dir, result_filename)}")
        
        # Clean up resources
        try:
            # Give some time for async tasks to complete
            time.sleep(0.5)
            
            # Close images if they were opened
            for img in images:
                if hasattr(img, 'close'):
                    img.close()
                    
        except Exception as cleanup_error:
            print(f"Warning: Error during cleanup: {cleanup_error}")
        
        return local_md_dir
        
    except Exception as e:
        raise RuntimeError(f"Single task recognition failed: {str(e)}")

def parse_pdf(input_file, output_dir, MonkeyOCR_model):
    """
    Parse PDF file and save results
    
    Args:
        input_file: Input PDF file path
        output_dir: Output directory
        MonkeyOCR_model: Pre-initialized model instance
    """
    print(f"Starting to parse file: {input_file}")
    
    # Check if input file exists
    if not os.path.exists(input_file):
        raise FileNotFoundError(f"Input file does not exist: {input_file}")
    
    # Get filename
    name_without_suff = '.'.join(os.path.basename(input_file).split(".")[:-1])
    
    # Prepare output directory
    local_image_dir = os.path.join(output_dir, name_without_suff, "images")
    local_md_dir = os.path.join(output_dir, name_without_suff)
    image_dir = os.path.basename(local_image_dir)
    os.makedirs(local_image_dir, exist_ok=True)
    os.makedirs(local_md_dir, exist_ok=True)
    
    print(f"Output dir: {local_md_dir}")
    image_writer = FileBasedDataWriter(local_image_dir)
    md_writer = FileBasedDataWriter(local_md_dir)
    
    # Read file content
    reader = FileBasedDataReader()
    file_bytes = reader.read(input_file)
    
    # Create dataset instance
    file_extension = input_file.split(".")[-1].lower()
    if file_extension == "pdf":
        ds = PymuDocDataset(file_bytes)
    else:
        ds = ImageDataset(file_bytes)
    
    # Start inference
    print("Performing document parsing...")
    start_time = time.time()
    
    infer_result = ds.apply(doc_analyze_llm, MonkeyOCR_model=MonkeyOCR_model)
    
    # Pipeline processing
    pipe_result = infer_result.pipe_ocr_mode(image_writer, MonkeyOCR_model=MonkeyOCR_model)
    
    parsing_time = time.time() - start_time
    print(f"Parsing time: {parsing_time:.2f}s")

    infer_result.draw_model(os.path.join(local_md_dir, f"{name_without_suff}_model.pdf"))
    
    pipe_result.draw_layout(os.path.join(local_md_dir, f"{name_without_suff}_layout.pdf"))

    pipe_result.draw_span(os.path.join(local_md_dir, f"{name_without_suff}_spans.pdf"))

    pipe_result.dump_md(md_writer, f"{name_without_suff}.md", image_dir)
    
    pipe_result.dump_content_list(md_writer, f"{name_without_suff}_content_list.json", image_dir)

    pipe_result.dump_middle_json(md_writer, f'{name_without_suff}_middle.json')
    
    print("Results saved to ", local_md_dir)
    return local_md_dir

def enhanced_recognition(input_file, output_dir, MonkeyOCR_model):
    """
    Enhanced recognition that combines text extraction with table structure preservation
    
    Args:
        input_file: Input file path
        output_dir: Output directory
        MonkeyOCR_model: Pre-initialized model instance
    """
    print(f"Starting enhanced recognition for: {input_file}")
    
    # Check if input file exists
    if not os.path.exists(input_file):
        raise FileNotFoundError(f"Input file does not exist: {input_file}")
    
    # Get filename
    name_without_suff = '.'.join(os.path.basename(input_file).split(".")[:-1])
    
    # Prepare output directory
    local_md_dir = os.path.join(output_dir, name_without_suff)
    local_image_dir = os.path.join(local_md_dir, "images")
    image_dir = os.path.basename(local_image_dir)
    os.makedirs(local_image_dir, exist_ok=True)
    os.makedirs(local_md_dir, exist_ok=True)
    
    print(f"Output dir: {local_md_dir}")
    image_writer = FileBasedDataWriter(local_image_dir)
    md_writer = FileBasedDataWriter(local_md_dir)
    
    # Read file content
    reader = FileBasedDataReader()
    file_bytes = reader.read(input_file)
    
    # Create dataset instance
    file_extension = input_file.split(".")[-1].lower()
    if file_extension == "pdf":
        ds = PymuDocDataset(file_bytes)
    else:
        ds = ImageDataset(file_bytes)
    
    # First, run table recognition to get table structure
    print("Step 1: Detecting tables...")
    md_with_tables = ""
    try:
        # Run standard parsing to get layout and table structure
        infer_result = ds.apply(doc_analyze_llm, MonkeyOCR_model=MonkeyOCR_model)
        pipe_result = infer_result.pipe_ocr_mode(image_writer, MonkeyOCR_model=MonkeyOCR_model)
        
        # Save intermediate results
        try:
            infer_result.draw_model(os.path.join(local_md_dir, f"{name_without_suff}_model.pdf"))
            pipe_result.draw_layout(os.path.join(local_md_dir, f"{name_without_suff}_layout.pdf"))
        except Exception as e:
            print(f"Warning: Could not save intermediate visualizations: {str(e)}")
        
        # Get the markdown with tables
        try:
            md_with_tables = pipe_result.get_markdown(image_dir)
            # Save the table-focused markdown
            md_writer.write(f"{name_without_suff}_tables.md", md_with_tables.encode('utf-8'))
            print(f"Table structure saved to: {os.path.join(local_md_dir, name_without_suff)}_tables.md")
        except Exception as e:
            print(f"Warning: Could not extract table markdown: {str(e)}")
    except Exception as e:
        print(f"Warning: Table detection failed: {str(e)}")
    
    # Now run text recognition to get all text
    print("Step 2: Extracting all text...")
    text_response = ""
    try:
        # Load image
        from PIL import Image
        image = Image.open(input_file)
        
        # Use chat model for text recognition - handle different API interfaces
        instruction = TASK_INSTRUCTIONS['text']
        
        # Check which method is available and use it
        if hasattr(MonkeyOCR_model.chat_model, 'inference'):
            text_response = MonkeyOCR_model.chat_model.inference(image, instruction)
        elif hasattr(MonkeyOCR_model.chat_model, 'chat'):
            text_response = MonkeyOCR_model.chat_model.chat(image, instruction)
        else:
            # Try the batch_inference method with a single image
            text_response = MonkeyOCR_model.chat_model.batch_inference([image], [instruction])[0]
        
        # Save text result
        text_filename = f"{name_without_suff}_text_result.md"
        md_writer.write(text_filename, text_response.encode('utf-8'))
        print(f"Text extraction saved to: {os.path.join(local_md_dir, text_filename)}")
    except Exception as e:
        print(f"Warning: Text extraction failed: {str(e)}")
        # Try single task recognition as fallback
        try:
            print("Trying single task text recognition as fallback...")
            single_task_recognition(input_file, local_md_dir, MonkeyOCR_model, 'text')
            # Read the result file
            text_result_path = os.path.join(local_md_dir, f"{name_without_suff}_text_result.md")
            if os.path.exists(text_result_path):
                with open(text_result_path, 'r', encoding='utf-8') as f:
                    text_response = f.read()
        except Exception as fallback_error:
            print(f"Fallback text extraction also failed: {str(fallback_error)}")
    
    # Combine results - if we have tables, use the table structure and fill in missing text
    print("Step 3: Combining results...")
    if md_with_tables and text_response:
        # Extract tables from md_with_tables
        import re
        # Look for image references and LaTeX blocks which typically represent tables
        table_pattern = r'(!\[.*?\]\(images/.*?\))|(```.*?```)|((\$\$|\$).*?(\$\$|\$))'
        tables = re.findall(table_pattern, md_with_tables, re.DOTALL)
        
        # Flatten the list of tuples and filter empty strings
        table_sections = []
        for t in tables:
            for item in t:
                if item and len(item.strip()) > 0:
                    table_sections.append(item)
        
        # Create combined markdown
        combined_md = text_response
        
        # Insert tables at appropriate positions
        if table_sections:
            print(f"Found {len(table_sections)} table sections to insert")
            # Simple approach: just append tables at the end if we can't find good insertion points
            if "\n\n" not in combined_md:
                combined_md += "\n\n" + "\n\n".join(table_sections)
            else:
                # Try to insert tables at paragraph breaks
                paragraphs = combined_md.split("\n\n")
                # Distribute tables roughly evenly
                tables_per_break = max(1, len(paragraphs) // (len(table_sections) + 1))
                
                new_md = []
                table_idx = 0
                
                for i, para in enumerate(paragraphs):
                    new_md.append(para)
                    # Insert a table after every tables_per_break paragraphs
                    if i > 0 and i % tables_per_break == 0 and table_idx < len(table_sections):
                        new_md.append(table_sections[table_idx])
                        table_idx += 1
                
                # Add any remaining tables at the end
                while table_idx < len(table_sections):
                    new_md.append(table_sections[table_idx])
                    table_idx += 1
                
                combined_md = "\n\n".join(new_md)
        
        # Save combined result
        combined_filename = f"{name_without_suff}_combined.md"
        md_writer.write(combined_filename, combined_md.encode('utf-8'))
        
        # Also save as the main result
        md_writer.write(f"{name_without_suff}.md", combined_md.encode('utf-8'))
        
        print(f"Combined result saved to: {os.path.join(local_md_dir, combined_filename)}")
    else:
        # If one of the approaches failed, use whatever we have
        final_result = md_with_tables or text_response
        if final_result:
            md_writer.write(f"{name_without_suff}.md", final_result.encode('utf-8'))
            print(f"Saved available results to: {os.path.join(local_md_dir, name_without_suff)}.md")
        else:
            # If both approaches failed, create a simple error message
            error_message = "Failed to extract content from this document. Please try a different approach."
            md_writer.write(f"{name_without_suff}.md", error_message.encode('utf-8'))
            print(f"No content could be extracted. Created error message file.")
    
    print("Results saved to ", local_md_dir)
    return local_md_dir

def main():
    parser = argparse.ArgumentParser(
        description="PDF Document Parsing Tool",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Usage examples:
  python parse.py input.pdf                  # Parse single PDF file
  python parse.py input.pdf -o ./output      # Parse single PDF with custom output dir
  python parse.py /path/to/folder            # Parse all files in folder
  python parse.py /path/to/folder -t text    # Single task recognition for all files in folder
  python parse.py input.pdf -c model_configs.yaml
  python parse.py image.jpg -t text          # Single task: text recognition
  python parse.py image.jpg -t formula       # Single task: formula recognition  
  python parse.py image.jpg -t table         # Single task: table recognition
  python parse.py document.pdf -t text       # Single task: text recognition from all PDF pages (with warning)
  python parse.py image.jpg -m enhanced      # Enhanced mode: combines text and table recognition
        """
    )
    
    parser.add_argument(
        "input_path",
        help="Input PDF/image file path or folder path"
    )
    
    parser.add_argument(
        "-o", "--output",
        default="./output",
        help="Output directory (default: ./output)"
    )
    
    parser.add_argument(
        "-c", "--config",
        default="model_configs.yaml",
        help="Configuration file path (default: model_configs.yaml)"
    )
    
    parser.add_argument(
        "-t", "--task",
        choices=['text', 'formula', 'table'],
        help="Single task recognition type (text/formula/table). Supports both image and PDF files."
    )
    
    parser.add_argument(
        "-m", "--mode",
        choices=['enhanced'],
        help="Processing mode: 'enhanced' combines text and table recognition"
    )
    
    args = parser.parse_args()
    
    MonkeyOCR_model = None
    
    try:
        # Check if input path is a directory or file
        if os.path.isdir(args.input_path):
            # Process folder
            result_dir = parse_folder(
                args.input_path,
                args.output,
                args.config,
                args.task
            )
            
            if args.task:
                print(f"\n✅ Folder processing with single task ({args.task}) recognition completed! Results saved in: {result_dir}")
            else:
                print(f"\n✅ Folder processing completed! Results saved in: {result_dir}")
        elif os.path.isfile(args.input_path):
            # Process single file - initialize model for single file processing
            print("Loading model...")
            MonkeyOCR_model = MonkeyOCR(args.config)
            
            if args.mode == 'enhanced':
                result_dir = enhanced_recognition(
                    args.input_path,
                    args.output,
                    MonkeyOCR_model
                )
                print(f"\n✅ Enhanced recognition completed! Results saved in: {result_dir}")
            elif args.task:
                result_dir = single_task_recognition(
                    args.input_path,
                    args.output,
                    MonkeyOCR_model,
                    args.task
                )
                print(f"\n✅ Single task ({args.task}) recognition completed! Results saved in: {result_dir}")
            else:
                result_dir = parse_pdf(
                    args.input_path,
                    args.output,
                    MonkeyOCR_model
                )
                print(f"\n✅ Parsing completed! Results saved in: {result_dir}")
        else:
            raise FileNotFoundError(f"Input path does not exist: {args.input_path}")
            
    except Exception as e:
        print(f"\n❌ Processing failed: {str(e)}", file=sys.stderr)
        sys.exit(1)
    finally:
        # Clean up resources
        try:
            if MonkeyOCR_model is not None:
                # Clean up model resources if needed
                if hasattr(MonkeyOCR_model, 'chat_model') and hasattr(MonkeyOCR_model.chat_model, 'close'):
                    MonkeyOCR_model.chat_model.close()
                    
            # Give time for async tasks to complete before exiting
            time.sleep(1.0)
            
            if dist.is_initialized():
                dist.destroy_process_group()
                
        except Exception as cleanup_error:
            print(f"Warning: Error during final cleanup: {cleanup_error}")


if __name__ == "__main__":
    main()
